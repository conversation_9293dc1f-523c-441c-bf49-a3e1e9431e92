# 项目: cognitive-complexity CLI

- **描述**: Node.js CLI 工具, 使用 Bun 开发。
- **监控文件**: `_.ts, _.tsx, _.html, _.css, _.js, _.jsx, package.json`
- **入口**: `src/index.ts`
- **二进制名**: `complexity`
- **路径别名**: `@/*` -> `src/*`

## CRITICAL: 环境与兼容性

- **开发环境**: Bun
- **生产/用户环境**: Node.js 18+ (必须 100% 兼容)
- **用户约束**: 通过 `npm`/`npx` 运行，Bun 不是用户依赖。
- **API 禁令**: 严禁任何 `Bun.*` API (`Bun.file`, `Bun.serve`, `Bun.$` 等)。
- **必需 API**: 使用 Node.js 原生 API (`fs`, `path`) 及标准 npm 包 (`commander`, `cosmiconfig`, `@swc/core`)。
- **构建目标**: Node.js 兼容的 ESM 模块。

## 架构与设计

- **核心范式**: 函数式编程 (纯函数、不可变数据、函数组合)。
- **代码行数限制**:
  - `> 1000` 行: 警告并规划模块拆分。
  - `> 1500` 行: 硬性上限，必须重构。
- **类使用原则**:
  - **允许 (状态/生命周期)**: 错误类、缓存管理器、插件系统、性能监控器。
  - **禁止 (逻辑/数据)**: 业务计算、数据转换、配置管理。
- **当前架构**: IoC (`CalculatorFactory`)，轻量级 AST 访问者 (`ComplexityVisitor`)，函数式 API (`analyzeFile`)，资源自动回收 (`dispose`)。

## 开发工作流

- **向后兼容**: 无需考虑, 也就不需要保留 @deprecated 这样的注释和逻辑。
- **类型安全**: 严格，禁止 `as any`，必须修复类型根因。
- **类型检查**: `bun typecheck` (全量), `bun typecheck /path/to` (单文件)。
- **测试**: `pnpm test` (全量), `pnpm test src/__test__/path/to` (单文件)。
- **临时脚本**: `@./temp/`。
- **不需要保持向后兼容性**。

## API 接口

1.  **函数式 (推荐)**: `analyzeFile(path, opts)`, `analyzeCode(code, path, opts)`。
2.  **静态 (兼容)**: `ComplexityCalculator.analyze(code)`, `ComplexityCalculator.analyzeFile(path)`。
3.  **实例 (批量/高级配置)**: `new Calculator(opts, factory)`，使用后需调用 `dispose()`。

## 命令

- `bun install`: 安装依赖
- `bun run dev`: 监听开发
- `bun run start`: 运行
- `bun run build`: 构建到 `dist/`
- `bun test`: 运行测试 (Vitest)

## 测试

- **CI/CD 兼容性**: 测试用例必须能在 Node.js 测试环境中运行。