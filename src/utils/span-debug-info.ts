/**
 * SWC Span 调试信息工具函数集
 * 
 * 提供 span 调试信息的格式化和提取功能，用于在调试模式下显示 SWC 原始 span 信息。
 * 遵循函数式编程范式，所有函数都是纯函数，无副作用。
 * 
 * <AUTHOR> Code
 * @version 1.0.0
 */

import chalk from 'chalk';
import { getGlobalFileCache } from './file-cache';
import { DetailStep, Position } from '../core/types';
import type { CLIOptions } from '../config/types';

/**
 * Span 调试信息数据结构
 * 内部使用的调试信息结构，保持不可变性
 */
export interface SpanDebugInfo {
  /** SWC 原始 span 位置 */
  readonly span: { start: number; end: number };
  /** AST 节点类型 */
  readonly nodeType: string;
  /** 从源码中提取的代码片段 */
  readonly sourceSnippet: string;
  /** 计算出的行列位置信息 */
  readonly position: { line: number; column: number };
  /** 是否有有效的 span 信息 */
  readonly hasValidSpan: boolean;
  /** 错误信息（如果有） */
  readonly errorMessage?: string;
}

/**
 * 从源代码中提取 span 对应的代码片段
 * 
 * @param sourceCode - 完整的源代码内容
 * @param span - SWC span 位置信息
 * @returns 提取的代码片段
 * 
 * @example
 * ```typescript
 * const snippet = extractSpanSourceCode(sourceCode, { start: 100, end: 120 });
 * console.log(snippet); // "if (condition) {"
 * ```
 */
export function extractSpanSourceCode(
  sourceCode: string, 
  span: { start: number; end: number }
): string {
  if (!sourceCode || typeof sourceCode !== 'string') {
    return '';
  }

  if (!span || typeof span.start !== 'number' || typeof span.end !== 'number') {
    return '';
  }

  // 确保 span 范围有效
  const start = Math.max(0, Math.min(span.start, sourceCode.length));
  const end = Math.max(start, Math.min(span.end, sourceCode.length));

  if (start >= sourceCode.length || end <= start) {
    return '';
  }

  return sourceCode.slice(start, end);
}

/**
 * 安全转义代码片段，防止终端注入攻击
 * 
 * @param snippet - 原始代码片段
 * @param maxLength - 最大长度限制，默认为 50
 * @returns 经过转义和截取的安全代码片段
 * 
 * @example
 * ```typescript
 * const safe = sanitizeCodeSnippet('console.log("hello\nworld")', 20);
 * console.log(safe); // "console.log(\"hello\\n..."
 * ```
 */
export function sanitizeCodeSnippet(snippet: string, maxLength: number = 50): string {
  if (!snippet || typeof snippet !== 'string') {
    return '';
  }

  // 转义特殊字符，防止终端注入
  const escaped = snippet
    .replace(/\\/g, '\\\\')  // 反斜杠
    .replace(/\n/g, '\\n')   // 换行符
    .replace(/\r/g, '\\r')   // 回车符
    .replace(/\t/g, '\\t')   // 制表符
    .replace(/\u001b/g, '\\u001b') // ESC 字符
    .replace(/[\u0000-\u001f\u007f-\u009f]/g, (match) => {
      // 替换其他控制字符
      return `\\u${match.charCodeAt(0).toString(16).padStart(4, '0')}`;
    });

  // 截取长度并添加省略号
  if (escaped.length <= maxLength) {
    return escaped;
  }

  return escaped.slice(0, maxLength - 3) + '...';
}

/**
 * 格式化位置信息（行号和列号）
 * 
 * @param span - SWC span 位置信息
 * @param position - 计算得出的行列位置
 * @returns 格式化的位置信息字符串
 * 
 * @example
 * ```typescript
 * const info = formatPositionInfo(
 *   { start: 100, end: 120 },
 *   { line: 5, column: 10 }
 * );
 * console.log(info); // "span: 100-120, 行: 5, 列: 10"
 * ```
 */
export function formatPositionInfo(
  span: { start: number; end: number },
  position: { line: number; column: number }
): string {
  const spanInfo = `span: ${span.start}-${span.end}`;
  const positionInfo = `行: ${position.line}, 列: ${position.column}`;
  return `${spanInfo}, ${positionInfo}`;
}

/**
 * 从 span 位置计算行列号
 * 基于源代码内容和字节偏移计算准确的行列位置
 * 
 * 该函数遍历源代码字符，统计换行符来计算准确的行列位置。
 * 这比简单的字符串分割方式更高效，特别是对于大文件。
 * 
 * @param sourceCode - 完整的源代码内容
 * @param offset - 字节偏移位置（SWC span.start 或 span.end）
 * @returns 计算得出的行列位置（行号从1开始，列号从0开始）
 * 
 * @internal 此函数仅供模块内部使用
 */
function calculatePositionFromOffset(sourceCode: string, offset: number): Position {
  // 边界条件检查：无效输入返回默认位置
  if (!sourceCode || offset < 0 || offset > sourceCode.length) {
    return { line: 1, column: 0 };
  }

  let line = 1;    // 行号从1开始
  let column = 0;  // 列号从0开始

  // 遍历到指定偏移位置，统计行列号
  for (let i = 0; i < offset && i < sourceCode.length; i++) {
    if (sourceCode[i] === '\n') {
      line++;      // 遇到换行符，行号递增
      column = 0;  // 列号重置为0
    } else {
      column++;    // 普通字符，列号递增
    }
  }

  return { line, column };
}

/**
 * 生成降级调试信息
 * 当无法获取完整调试信息时的降级处理
 * 
 * 此函数确保即使在异常情况下，用户仍能获得基本的调试信息，
 * 遵循优雅降级的错误处理原则。
 * 
 * @param step - 详细步骤信息
 * @param errorMessage - 错误信息描述
 * @returns 降级格式的调试信息字符串
 * 
 * @internal 此函数仅供模块内部使用
 */
function formatFallbackDebugInfo(step: DetailStep, errorMessage: string): string {
  const lines: string[] = [];
  
  // 标题：标识这是降级模式的调试信息
  lines.push(chalk.gray('🔍 SWC Span 调试信息 (降级模式):'));
  
  // 节点类型：始终可用的基本信息
  lines.push(`  ${chalk.gray('节点类型:')} ${step.nodeType || '未知'}`);
  
  // span 位置：如果可用则显示，否则显示提示信息
  if (step.span) {
    lines.push(`  ${chalk.gray('原始位置:')} ${step.span.start}-${step.span.end}`);
  } else {
    lines.push(`  ${chalk.gray('原始位置:')} 无可用 span 信息`);
  }
  
  // 错误原因：帮助用户理解为什么使用降级模式
  lines.push(`  ${chalk.gray('错误:')} ${errorMessage}`);
  
  return lines.join('\n');
}

/**
 * 主要格式化函数：生成完整的 SWC Span 调试信息
 * 
 * 该函数是本模块的核心，负责收集并格式化所有 span 相关的调试信息。
 * 采用三层错误处理策略：优雅降级、边界条件处理、主流程保护。
 * 
 * @param step - 详细步骤信息，包含 span 和节点类型
 * @param filePath - 源文件路径
 * @param options - CLI 选项配置
 * @returns Promise<string | null> - 格式化的调试信息字符串，失败时返回 null
 * 
 * @example
 * ```typescript
 * const debugInfo = await formatSpanDebugInfo(step, '/path/to/file.ts', { debug: true });
 * if (debugInfo) {
 *   console.log(debugInfo);
 * }
 * ```
 */
export async function formatSpanDebugInfo(
  step: DetailStep, 
  filePath: string, 
  options?: CLIOptions
): Promise<string | null> {
  try {
    // === 第一层验证：基本参数检查 ===
    if (!step || !filePath) {
      return null;  // 缺少必要参数，直接返回
    }

    // === 第二层验证：span 信息可用性检查 ===
    if (!step.span) {
      return formatFallbackDebugInfo(step, '无可用 span 信息');
    }

    // === 第三层验证：span 数据完整性检查 ===
    if (typeof step.span.start !== 'number' || typeof step.span.end !== 'number') {
      return formatFallbackDebugInfo(step, 'span 位置数据无效');
    }

    // === 文件内容获取：使用缓存机制优化性能 ===
    const fileCache = getGlobalFileCache();
    let sourceCode: string;
    
    try {
      sourceCode = await fileCache.getFileContent(filePath);
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      return formatFallbackDebugInfo(step, `无法读取源文件内容: ${errorMsg}`);
    }

    // === span 范围验证：确保 span 在文件内容范围内 ===
    if (step.span.start < 0 || step.span.end > sourceCode.length || step.span.start >= step.span.end) {
      const warningMsg = `span 范围超出文件范围 (文件长度: ${sourceCode.length})`;
      return formatFallbackDebugInfo(step, warningMsg);
    }

    // === 代码片段提取和安全转义 ===
    const sourceSnippet = extractSpanSourceCode(sourceCode, step.span);
    const sanitizedSnippet = sanitizeCodeSnippet(sourceSnippet, 50);

    // === 位置计算：将字节偏移转换为行列号 ===
    let position: Position;
    try {
      position = calculatePositionFromOffset(sourceCode, step.span.start);
    } catch (error) {
      // 位置计算失败时，fallback 到现有的行列信息
      position = { line: step.line || 1, column: step.column || 0 };
    }

    // === 构建调试信息对象：所有数据准备完毕 ===
    const debugInfo: SpanDebugInfo = {
      span: { start: step.span.start, end: step.span.end },
      nodeType: step.nodeType || '未知',
      sourceSnippet: sanitizedSnippet,
      position,
      hasValidSpan: true
    };

    // === 最终格式化输出 ===
    return formatDebugInfoOutput(debugInfo);

  } catch (error) {
    // 最外层错误捕获：确保调试信息生成错误不影响主要功能
    const errorMsg = error instanceof Error ? error.message : '未知错误';
    return formatFallbackDebugInfo(step, `调试信息生成失败: ${errorMsg}`);
  }
}

/**
 * 格式化调试信息输出
 * 将 SpanDebugInfo 对象转换为用户友好的字符串格式
 * 
 * 该函数负责最终的视觉呈现，使用统一的颜色方案和格式布局，
 * 确保调试信息与现有的输出风格保持一致。
 * 
 * @param debugInfo - 调试信息对象
 * @returns 格式化的调试信息字符串
 * 
 * @internal 此函数仅供模块内部使用
 */
function formatDebugInfoOutput(debugInfo: SpanDebugInfo): string {
  const lines: string[] = [];
  
  // 标题行：使用灰色和图标标识调试信息区域
  lines.push(chalk.gray('🔍 SWC Span 调试信息:'));
  
  // 节点类型：AST 节点的具体类型，用白色突出显示
  lines.push(`  ${chalk.gray('节点类型:')} ${chalk.white(debugInfo.nodeType)}`);
  
  // 位置信息：包含 span 和计算出的行列号
  const spanInfo = formatPositionInfo(debugInfo.span, debugInfo.position);
  lines.push(`  ${chalk.gray('位置信息:')} ${chalk.white(spanInfo)}`);
  
  // 代码片段：实际的源代码内容，用青色显示便于识别
  if (debugInfo.sourceSnippet) {
    lines.push(`  ${chalk.gray('代码片段:')} ${chalk.cyan(`"${debugInfo.sourceSnippet}"`)}`);
  } else {
    // 无法提取代码片段时的提示，用黄色表示警告
    lines.push(`  ${chalk.gray('代码片段:')} ${chalk.yellow('无法提取')}`);
  }
  
  return lines.join('\n');
}